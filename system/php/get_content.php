<?php

header("Access-Control-Allow-Origin: '*'");

require_once('./client_db.php');
require_once('./controls.php');
function formatPrice($price) {
    return number_format($price, 0, '', ' ');
}

$language = $_GET['language'];
$view_type = $_GET['view_type'];

$budynek = $_GET['budynek'];
$pietro = $_GET['pietro'];
$status = $_GET['status'];

$conditions = []; 

if ($budynek != 'x') {
    $conditions[] = "budynek = '" . $budynek . "'";
}
if ($pietro != 'x') {
    $conditions[] = "pietro = '" . $pietro . "'";
}
if ($status != 'x') {
    $conditions[] = "status = '" . $status . "'";
}

$sql_filters = '';
if (!empty($conditions)) {
    $sql_filters = ' WHERE ' . implode(' AND ', $conditions);
}

$sql = 'SELECT * FROM system_locals';
$sql .= $sql_filters . ' ORDER BY 
         LEFT(lokal, 1),
         CAST(REGEXP_REPLACE(lokal, "[^0-9]", "") AS UNSIGNED);'; 
$result = $conn->query($sql);
$row_file = "";

//-----main switch to attach new views
switch ($view_type) {
    case '1':
        $row_file = '../system/templates/tab1.php';
        break;
    case '2':
        require_once('../templates/tab' . $view_type . '_css.php');
        $row_file = "../templates/tab" . $view_type . "_row.php";
        break;
    default:
        echo 'Nieznana opcja';
        exit; // Zmieniłem 'return' na 'exit', aby przerwać wykonywanie skryptu
}
//------------------------------------

echo "<div id=\"system_content\">";
    while ($row = $result->fetch_array()) {
        require($row_file);
    };
echo "</div>";
echo "<div id='developer_status' style='display:none'></div>";


mysqli_close($conn);
