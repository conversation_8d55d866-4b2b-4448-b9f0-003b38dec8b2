<?php

use JetBrains\PhpStorm\Language; 

    echo "<div id='t". $row[2] ."' class='T1_row'>
            <div class='T1_col T1_col_1'>
                <p>". $row[2] ."</p>
            </div>
            <div class='T1_col T1_col_2'>
                <div class='T1_container description'>
                    <script>$('#t". $row[2] ." .T1_col_2 .T1_container').html(decodeText('". $row[6] ."', '". $language ."'));</script>
                </div>
            </div>
            <div class='T1_col T1_col_3'>
                <div class='T1_container'>
                    <h5><strong>Cena brutto</strong></h5>
                    <p class='price'>". formatPrice(round($row[10], 0)) ." zł</</p>
                </div>
            </div>
            <div class='T1_col T1_col_4'>
                <div class='T1_container'>
                    <h5><strong>Karta lokalu</strong></h5>
                    <p><a href='". $row[13] ."' target='_blank' rel='noopener noreferrer' title='Karta lokalu " . $row[2] . "'><strong><img decoding='async' src='../system/templates/img/pdf_1.png' width='64' height='64'></strong></a></p>
                </div>
            </div>
        </div>";
?>
